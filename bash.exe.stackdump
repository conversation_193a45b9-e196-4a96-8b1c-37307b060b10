Stack trace:
Frame         Function      Args
0007FFFFAA60  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF9960) msys-2.0.dll+0x2118E
0007FFFFAA60  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFAA60  0002100469F2 (00021028DF99, 0007FFFFA918, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFAA60  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFAA60  00021006A545 (0007FFFFAA70, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFAA70, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD2FC20000 ntdll.dll
7FFD2EA40000 KERNEL32.DLL
7FFD2D1C0000 KERNELBASE.dll
7FFD2EB10000 USER32.dll
7FFD2D590000 win32u.dll
7FFD2FB30000 GDI32.dll
7FFD2CDB0000 gdi32full.dll
7FFD2CEF0000 msvcp_win.dll
7FFD2D7D0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD2F000000 advapi32.dll
7FFD2E2A0000 msvcrt.dll
7FFD2E4F0000 sechost.dll
7FFD2E350000 RPCRT4.dll
7FFD2C4A0000 CRYPTBASE.DLL
7FFD2CFA0000 bcryptPrimitives.dll
7FFD2FA70000 IMM32.DLL
